<?php $__env->startSection('js'); ?>
    <!-- jQuery (required for DataTables plugin) -->
    <script src="<?php echo e(asset('js/lib/jquery.min.js')); ?>"></script>
    
    <!-- Select2 pour améliorer la sélection multiple -->
    <script src="<?php echo e(asset('js/plugins/select2/js/select2.full.min.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('css'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('js/plugins/select2/css/select2.min.css')); ?>">
<?php $__env->stopSection(); ?>

<div wire:ignore.self>
    <?php echo $__env->make('livewire.deraq.resultat.liste', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
</div>

<script>
    // Notification handler
    window.addEventListener("showSuccessMessage", event => {
        One.helpersOnLoad(['jq-notify']);
        One.helpers('jq-notify', {
            type: event.detail.type || 'success',
            icon: event.detail.icon || 'fa fa-check me-1',
            message: event.detail.message || 'Opération effectuée avec succès!'
        });
    });
    
    // Error notification handler
    window.addEventListener("show-error", event => {
        One.helpersOnLoad(['jq-notify']);
        One.helpers('jq-notify', {
            type: 'danger',
            icon: 'fa fa-times me-1',
            message: event.detail.message || 'Une erreur est survenue!'
        });
    });
    
    // Scroll to results
    window.addEventListener("scroll-to-results", () => {
        setTimeout(() => {
            const resultsSection = document.querySelector('.block-header-default');
            if (resultsSection) {
                resultsSection.scrollIntoView({ behavior: 'smooth' });
            }
        }, 100);
    });

    // Select2 initialization
    document.addEventListener('livewire:load', function () {
        initSelect2();
        
        // Reinitialize Select2 when Livewire refreshes the DOM
        Livewire.hook('message.processed', (message, component) => {
            initSelect2();
        });
        
        // Reset event
        window.addEventListener('reset-select2', event => {
            resetSelect2();
        });
    });

    function initSelect2() {
        // Parcours multi-select
        if (document.getElementById('parcour-select')) {
            $('#parcour-select').select2({
                placeholder: 'Sélectionnez les parcours',
                allowClear: true,
                closeOnSelect: false, // Empêche la fermeture après sélection
                width: '100%'
            }).on('change', function (e) {
                // Sync selected values with Livewire
                window.livewire.find('<?php echo e($_instance->id); ?>').set('newResults.parcour_id', $(this).val() || []);
            });
        }

        // Semestre multi-select
        if (document.getElementById('semestre-select')) {
            $('#semestre-select').select2({
                placeholder: 'Sélectionnez les semestres',
                allowClear: true,
                closeOnSelect: false, // Empêche la fermeture après sélection
                width: '100%'
            }).on('change', function (e) {
                // Sync selected values with Livewire
                window.livewire.find('<?php echo e($_instance->id); ?>').set('newResults.semestre_id', $(this).val() || []);
            });
        }
    }
    
    function resetSelect2() {
        // Reset all Select2 elements
        if ($('#parcour-select').length) {
            $('#parcour-select').val(null).trigger('change');
        }
        if ($('#semestre-select').length) {
            $('#semestre-select').val(null).trigger('change');
        }
    }
</script><?php /**PATH C:\xampp\htdocs\ImsaaProject\resources\views/livewire/deraq/resultat/index.blade.php ENDPATH**/ ?>