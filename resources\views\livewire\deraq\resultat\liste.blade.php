<div>
    <!-- Hero -->
    <div class="bg-body-light">
        <div class="content content-full">
            <h1 class="h3 fw-bold mb-2">
                Gestion des résultats
            </h1>


            <div class="row justify-content-center py-sm-3 py-md-5">
                <div class="col-md-3">
                    <!-- Parcours (Multi-select) -->
                    <div class="mb-4">
                        <label class="form-label" for="parcour-select">Parcours <span class="text-danger">*</span></label>
                        <div wire:ignore>
                            <select class="form-select @error('newResults.parcour_id') is-invalid @enderror"
                                id="parcour-select" name="parcour-select" multiple>
                                @foreach ($parcours as $parcour)
                                    <option value="{{ $parcour->id }}">{{ $parcour->sigle }}</option>
                                @endforeach
                            </select>
                        </div>
                        @error('newResults.parcour_id')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                </div>
                <div class="col-md-2">
                    <!-- Niveau -->
                    <div class="mb-4">
                        <label class="form-label" for="niveau-select">Niveau <span class="text-danger">*</span></label>
                        <select class="form-select @error('newResults.niveau_id') is-invalid @enderror"
                            wire:model="newResults.niveau_id" id="niveau-select" name="niveau-select">
                            <option value="0">Sélectionner un niveau</option>
                            @foreach ($niveaux as $niveau)
                                <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                            @endforeach
                        </select>
                        @error('newResults.niveau_id')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                </div>
                <div class="col-md-3">

                    <!-- Semestres (Multi-select) -->
                    <div class="mb-4">
                        <label class="form-label" for="semestre-select">Semestre <span
                                class="text-danger">*</span></label>
                        <div wire:ignore>
                            <select class="form-select @error('newResults.semestre_id') is-invalid @enderror"
                                id="semestre-select" name="semestre-select" multiple>
                                @foreach ($semestres as $semestre)
                                    <option value="{{ $semestre->id }}">{{ $semestre->nom }}</option>
                                @endforeach
                            </select>
                        </div>
                        @error('newResults.semestre_id')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                </div>
                <div class="col-md-2">

                    <!-- Année Universitaire -->
                    <div class="mb-4">
                        <label class="form-label" for="annee-select">Année Universitaire <span
                                class="text-danger">*</span></label>
                        <select class="form-select @error('newResults.annee_universitaire_id') is-invalid @enderror"
                            wire:model="newResults.annee_universitaire_id" id="annee-select" name="annee-select">
                            <option value="0">Sélectionner une année universitaire</option>
                            @foreach ($annees as $annee)
                                <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                            @endforeach
                        </select>
                        @error('newResults.annee_universitaire_id')
                            <span class="text-danger">{{ $message }}</span>
                        @enderror
                    </div>
                </div>
                <div class="col-md-2 align-self-end"> <!-- Use align-self-end to push buttons down -->
                    <!-- Action Buttons -->
                    <div class="mb-4">
                        <label class="form-label">&nbsp;</label> <!-- Add dummy label for alignment -->
                        <div class="d-flex gap-2"> <!-- Use flexbox for button grouping -->
                            <button type="button" wire:click="generateResults()" class="btn btn-primary w-100"
                                wire:loading.attr="disabled">
                                <span wire:loading wire:target="generateResults"
                                    class="spinner-border spinner-border-sm me-1" role="status"></span>
                                    <span wire:loading.remove wire:target="generateResults"><i class="fa fa-chart-bar me-1 opacity-75"></i></span>
                                 Générer
                            </button>
                            <button type="button" class="btn btn-alt-secondary" wire:click="resetResults"
                                wire:loading.attr="disabled" title="Réinitialiser">
                                <span wire:loading wire:target="resetResults"
                                    class="spinner-border spinner-border-sm me-1" role="status"></span>
                                <i class="fa fa-sync" wire:loading.remove wire:target="resetResults"></i>
                            </button>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <!-- END Hero -->

    <!-- Page Content -->
    <div class="content">
        <!-- Results Section -->

        <div class="block block-rounded">
            <div class="block-header block-header-default">
                <h3 class="block-title">
                    @if (!empty($notes))
                        Liste des résultats
                        @if ($current_parcours && count($current_parcours) > 0)
                            @foreach ($current_parcours as $index => $parcour)
                                {{ $index > 0 ? ' / ' : '' }}{{ $parcour->sigle }}
                            @endforeach
                        @endif

                        @if ($current_niveau)
                            {{ $current_niveau->nom }}
                        @endif

                        @if ($current_semestres && count($current_semestres) > 0)
                            @foreach ($current_semestres as $index => $semestre)
                                {{ $index > 0 ? ' / ' : '' }}{{ $semestre->nom }}
                            @endforeach
                        @endif

                        @if ($current_annee)
                            {{ $current_annee->nom }}
                        @endif
                    @else
                        Pas de résultat
                    @endif
                </h3>

                @if (!empty($notes))
                    <div class="block-options">
                        <button class="btn btn-sm btn-primary me-1" wire:click="pdfGenerate()"
                            wire:loading.attr="disabled">
                            <span wire:loading wire:target="pdfGenerate" class="spinner-border spinner-border-sm me-1"
                                role="status"></span>
                            <i class="fa fa-file-pdf me-1" wire:loading.remove wire:target="pdfGenerate"></i> Imprimer
                            en PDF
                        </button>
                    </div>
                @endif
            </div>

            <div class="block-content block-content-full">
                <table class="table table-bordered table-striped table-vcenter">
                    <thead>
                        <tr>
                            <th class="text-center" style="width: 60px;">Rang</th>
                            <th>Nom</th>
                            <th class="text-center" style="width: 100px;">Moyenne</th>
                            <th class="text-center" style="width: 120px;">Mention</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse ($notes as $index => $result)
                            <tr>
                                <td class="text-center fw-bold">
                                    @if(isset($result['rang']))
                                        {{ $result['rang'] }}
                                        @if($index > 0 && isset($notes[$index-1]['rang']) && $result['rang'] == $notes[$index-1]['rang'])
                                            <small class="text-muted">(ex æquo)</small>
                                        @endif
                                    @else
                                        {{ $index + 1 }}
                                    @endif
                                </td>
                                <td>{{ $result['nom'] }} {{ $result['prenom'] }}</td>
                                <td class="text-center fw-bold">{{ $result['moy'] }}</td>
                                <td class="text-center">
                                    @php
                                        $badgeClass = match ($result['mention']) {
                                            'Très bien' => 'success',
                                            'Bien' => 'info',
                                            'Assez-bien' => 'warning',
                                            default => 'secondary',
                                        };
                                    @endphp
                                    <span class="badge bg-{{ $badgeClass }}">
                                        {{ $result['mention'] }}
                                    </span>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="4">
                                    <div class="alert alert-warning d-flex align-items-center justify-content-between"
                                        role="alert">
                                        <div class="flex-grow-1 me-3">
                                            <p class="mb-0">
                                                Impossible de générer. Pas encore de note ajoutée !!
                                            </p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="fa fa-fw fa-exclamation-circle"></i>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        <!-- END Results Section -->
    </div>
    <!-- END Page Content -->
</div>
