<div>
    <!-- Hero -->
    <div class="bg-body-light">
        <div class="content content-full">
            <h1 class="h3 fw-bold mb-2">
                Gestion des résultats
            </h1>


            <div class="row justify-content-center py-sm-3 py-md-5">
                <div class="col-md-3">
                    <!-- Parcours (Multi-select) -->
                    <div class="mb-4">
                        <label class="form-label" for="parcour-select">Parcours <span class="text-danger">*</span></label>
                        <div wire:ignore>
                            <select class="form-select <?php $__errorArgs = ['newResults.parcour_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                id="parcour-select" name="parcour-select" multiple>
                                <?php $__currentLoopData = $parcours; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parcour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($parcour->id); ?>"><?php echo e($parcour->sigle); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <?php $__errorArgs = ['newResults.parcour_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="text-danger"><?php echo e($message); ?></span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
                <div class="col-md-2">
                    <!-- Niveau -->
                    <div class="mb-4">
                        <label class="form-label" for="niveau-select">Niveau <span class="text-danger">*</span></label>
                        <select class="form-select <?php $__errorArgs = ['newResults.niveau_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                            wire:model="newResults.niveau_id" id="niveau-select" name="niveau-select">
                            <option value="0">Sélectionner un niveau</option>
                            <?php $__currentLoopData = $niveaux; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $niveau): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($niveau->id); ?>"><?php echo e($niveau->nom); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php $__errorArgs = ['newResults.niveau_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="text-danger"><?php echo e($message); ?></span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
                <div class="col-md-3">

                    <!-- Semestres (Multi-select) -->
                    <div class="mb-4">
                        <label class="form-label" for="semestre-select">Semestre <span
                                class="text-danger">*</span></label>
                        <div wire:ignore>
                            <select class="form-select <?php $__errorArgs = ['newResults.semestre_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                id="semestre-select" name="semestre-select" multiple>
                                <?php $__currentLoopData = $semestres; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $semestre): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($semestre->id); ?>"><?php echo e($semestre->nom); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <?php $__errorArgs = ['newResults.semestre_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="text-danger"><?php echo e($message); ?></span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
                <div class="col-md-2">

                    <!-- Année Universitaire -->
                    <div class="mb-4">
                        <label class="form-label" for="annee-select">Année Universitaire <span
                                class="text-danger">*</span></label>
                        <select class="form-select <?php $__errorArgs = ['newResults.annee_universitaire_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                            wire:model="newResults.annee_universitaire_id" id="annee-select" name="annee-select">
                            <option value="0">Sélectionner une année universitaire</option>
                            <?php $__currentLoopData = $annees; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $annee): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($annee->id); ?>"><?php echo e($annee->nom); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <?php $__errorArgs = ['newResults.annee_universitaire_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="text-danger"><?php echo e($message); ?></span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
                <div class="col-md-2 align-self-end"> <!-- Use align-self-end to push buttons down -->
                    <!-- Action Buttons -->
                    <div class="mb-4">
                        <label class="form-label">&nbsp;</label> <!-- Add dummy label for alignment -->
                        <div class="d-flex gap-2"> <!-- Use flexbox for button grouping -->
                            <button type="button" wire:click="generateResults()" class="btn btn-primary w-100"
                                wire:loading.attr="disabled">
                                <span wire:loading wire:target="generateResults"
                                    class="spinner-border spinner-border-sm me-1" role="status"></span>
                                    <span wire:loading.remove wire:target="generateResults"><i class="fa fa-chart-bar me-1 opacity-75"></i></span>
                                 Générer
                            </button>
                            <button type="button" class="btn btn-alt-secondary" wire:click="resetResults"
                                wire:loading.attr="disabled" title="Réinitialiser">
                                <span wire:loading wire:target="resetResults"
                                    class="spinner-border spinner-border-sm me-1" role="status"></span>
                                <i class="fa fa-sync" wire:loading.remove wire:target="resetResults"></i>
                            </button>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <!-- END Hero -->

    <!-- Page Content -->
    <div class="content">
        <!-- Results Section -->

        <div class="block block-rounded">
            <div class="block-header block-header-default">
                <h3 class="block-title">
                    <?php if(!empty($notes)): ?>
                        Liste des résultats
                        <?php if($current_parcours && count($current_parcours) > 0): ?>
                            <?php $__currentLoopData = $current_parcours; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $parcour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php echo e($index > 0 ? ' / ' : ''); ?><?php echo e($parcour->sigle); ?>

                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>

                        <?php if($current_niveau): ?>
                            <?php echo e($current_niveau->nom); ?>

                        <?php endif; ?>

                        <?php if($current_semestres && count($current_semestres) > 0): ?>
                            <?php $__currentLoopData = $current_semestres; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $semestre): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php echo e($index > 0 ? ' / ' : ''); ?><?php echo e($semestre->nom); ?>

                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>

                        <?php if($current_annee): ?>
                            <?php echo e($current_annee->nom); ?>

                        <?php endif; ?>
                    <?php else: ?>
                        Pas de résultat
                    <?php endif; ?>
                </h3>

                <?php if(!empty($notes)): ?>
                    <div class="block-options">
                        <button class="btn btn-sm btn-primary me-1" wire:click="pdfGenerate()"
                            wire:loading.attr="disabled">
                            <span wire:loading wire:target="pdfGenerate" class="spinner-border spinner-border-sm me-1"
                                role="status"></span>
                            <i class="fa fa-file-pdf me-1" wire:loading.remove wire:target="pdfGenerate"></i> Imprimer
                            en PDF
                        </button>
                    </div>
                <?php endif; ?>
            </div>

            <div class="block-content block-content-full">
                <table class="table table-bordered table-striped table-vcenter">
                    <thead>
                        <tr>
                            <th class="text-center" style="width: 60px;">Rang</th>
                            <th>Nom</th>
                            <th class="text-center" style="width: 100px;">Moyenne</th>
                            <th class="text-center" style="width: 120px;">Mention</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $notes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $result): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td class="text-center fw-bold">
                                    <?php if(isset($result['rang'])): ?>
                                        <?php echo e($result['rang']); ?>

                                        <?php if($index > 0 && isset($notes[$index-1]['rang']) && $result['rang'] == $notes[$index-1]['rang']): ?>
                                            <small class="text-muted">(ex æquo)</small>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <?php echo e($index + 1); ?>

                                    <?php endif; ?>
                                </td>
                                <td><?php echo e($result['nom']); ?> <?php echo e($result['prenom']); ?></td>
                                <td class="text-center fw-bold"><?php echo e($result['moy']); ?></td>
                                <td class="text-center">
                                    <?php
                                        $badgeClass = match ($result['mention']) {
                                            'Très bien' => 'success',
                                            'Bien' => 'info',
                                            'Assez-bien' => 'warning',
                                            default => 'secondary',
                                        };
                                    ?>
                                    <span class="badge bg-<?php echo e($badgeClass); ?>">
                                        <?php echo e($result['mention']); ?>

                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="4">
                                    <div class="alert alert-warning d-flex align-items-center justify-content-between"
                                        role="alert">
                                        <div class="flex-grow-1 me-3">
                                            <p class="mb-0">
                                                Impossible de générer. Pas encore de note ajoutée !!
                                            </p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="fa fa-fw fa-exclamation-circle"></i>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- END Results Section -->
    </div>
    <!-- END Page Content -->
</div>
<?php /**PATH C:\xampp\htdocs\ImsaaProject\resources\views/livewire/deraq/resultat/liste.blade.php ENDPATH**/ ?>