{"__meta": {"id": "Xe9f612c40dd5dfab0a44feca8db14162", "datetime": "2025-07-15 09:58:47", "utime": **********.552663, "method": "POST", "uri": "/livewire/message/result", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 7, "messages": [{"message": "[09:58:47] LOG.info: Detected N+1 Query", "message_html": null, "is_string": false, "label": "info", "time": **********.534143, "collector": "log"}, {"message": "[09:58:47] LOG.info: Model: App\\Models\\Ue\r\nRelation: App\\Models\\Matiere\r\nNum-Called: 9\r\nCall-Stack:\r\n#17 \\app\\Http\\Livewire\\Result.php:180\r\n#18 \\app\\Http\\Livewire\\Result.php:151\r\n#19 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:36\r\n#20 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php:41\r\n#21 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:93\r\n#22 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:35\r\n#23 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php:149\r\n#24 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php:36\r\n#25 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:89\r\n#26 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:13\r\n#27 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#28 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#29 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#30 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#31 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#32 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#33 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n#34 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#35 \\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php:78\r\n#36 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php:49\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#39 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:121\r\n#40 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:64\r\n#41 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#42 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php:37\r\n#43 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php:67\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:116\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:797\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:776\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:740\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.546554, "collector": "log"}, {"message": "[09:58:47] LOG.info: Model: App\\Models\\Matiere\r\nRelation: App\\Models\\Note\r\nNum-Called: 27\r\nCall-Stack:\r\n#22 \\app\\Http\\Livewire\\Result.php:180\r\n#23 \\app\\Http\\Livewire\\Result.php:151\r\n#24 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:36\r\n#25 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php:41\r\n#26 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:93\r\n#27 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:35\r\n#28 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php:149\r\n#29 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php:36\r\n#30 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:89\r\n#31 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:13\r\n#32 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#33 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#34 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#35 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#36 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n#39 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#40 \\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php:78\r\n#41 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#42 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php:49\r\n#43 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:121\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:64\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php:37\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php:67\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.546877, "collector": "log"}, {"message": "[09:58:47] LOG.info: Model: App\\Models\\Ue\r\nRelation: App\\Models\\Matiere\r\nNum-Called: 14\r\nCall-Stack:\r\n#17 \\app\\Http\\Livewire\\Result.php:180\r\n#18 \\app\\Http\\Livewire\\Result.php:151\r\n#19 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:36\r\n#20 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php:41\r\n#21 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:93\r\n#22 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:35\r\n#23 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php:149\r\n#24 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php:36\r\n#25 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:89\r\n#26 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:13\r\n#27 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#28 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#29 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#30 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#31 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#32 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#33 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n#34 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#35 \\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php:78\r\n#36 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php:49\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#39 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:121\r\n#40 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:64\r\n#41 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#42 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php:37\r\n#43 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php:67\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:116\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:797\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:776\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:740\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.547113, "collector": "log"}, {"message": "[09:58:47] LOG.info: Model: App\\Models\\Matiere\r\nRelation: App\\Models\\Note\r\nNum-Called: 42\r\nCall-Stack:\r\n#22 \\app\\Http\\Livewire\\Result.php:180\r\n#23 \\app\\Http\\Livewire\\Result.php:151\r\n#24 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:36\r\n#25 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php:41\r\n#26 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:93\r\n#27 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:35\r\n#28 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php:149\r\n#29 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php:36\r\n#30 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:89\r\n#31 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:13\r\n#32 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#33 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#34 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#35 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#36 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n#39 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#40 \\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php:78\r\n#41 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#42 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php:49\r\n#43 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:121\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:64\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php:37\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php:67\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.547363, "collector": "log"}, {"message": "[09:58:47] LOG.info: Model: App\\Models\\Ue\r\nRelation: App\\Models\\Matiere\r\nNum-Called: 4\r\nCall-Stack:\r\n#17 \\app\\Http\\Livewire\\Result.php:180\r\n#18 \\app\\Http\\Livewire\\Result.php:151\r\n#19 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:36\r\n#20 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php:41\r\n#21 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:93\r\n#22 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:35\r\n#23 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php:149\r\n#24 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php:36\r\n#25 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:89\r\n#26 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:13\r\n#27 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#28 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#29 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#30 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#31 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#32 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#33 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n#34 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#35 \\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php:78\r\n#36 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php:49\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#39 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:121\r\n#40 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:64\r\n#41 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#42 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php:37\r\n#43 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php:67\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:116\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:797\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:776\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:740\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.547582, "collector": "log"}, {"message": "[09:58:47] LOG.info: Model: App\\Models\\Matiere\r\nRelation: App\\Models\\Note\r\nNum-Called: 12\r\nCall-Stack:\r\n#22 \\app\\Http\\Livewire\\Result.php:180\r\n#23 \\app\\Http\\Livewire\\Result.php:151\r\n#24 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:36\r\n#25 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php:41\r\n#26 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:93\r\n#27 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:35\r\n#28 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php:149\r\n#29 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php:36\r\n#30 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:89\r\n#31 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:13\r\n#32 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#33 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#34 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#35 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#36 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n#39 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#40 \\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php:78\r\n#41 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#42 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php:49\r\n#43 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:121\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:64\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php:37\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php:67\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.547844, "collector": "log"}]}, "time": {"start": 1752562714.719304, "end": **********.552724, "duration": 12.833419799804688, "duration_str": "12.83s", "measures": [{"label": "Booting", "start": 1752562714.719304, "relative_start": 0, "end": 1752562715.683464, "relative_end": 1752562715.683464, "duration": 0.9641599655151367, "duration_str": "964ms", "params": [], "collector": null}, {"label": "Application", "start": 1752562715.684199, "relative_start": 0.9648950099945068, "end": **********.552729, "relative_end": 5.0067901611328125e-06, "duration": 11.868529796600342, "duration_str": "11.87s", "params": [], "collector": null}]}, "memory": {"peak_usage": 29344528, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "livewire.deraq.resultat.index (\\resources\\views\\livewire\\deraq\\resultat\\index.blade.php)", "param_count": 14, "params": ["parcours", "niveaux", "semestres", "annees", "livewireLayout", "errors", "_instance", "newResults", "notes", "showResults", "current_parcours", "current_niveau", "current_semestres", "current_annee"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/resultat/index.blade.php&line=0"}, {"name": "livewire.deraq.resultat.liste (\\resources\\views\\livewire\\deraq\\resultat\\liste.blade.php)", "param_count": 16, "params": ["__env", "app", "errors", "_instance", "parcours", "niveaux", "semestres", "annees", "livewireLayout", "newResults", "notes", "showResults", "current_parcours", "current_niveau", "current_semestres", "current_annee"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/resultat/liste.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 141, "nb_failed_statements": 0, "accumulated_duration": 8.854209999999993, "accumulated_duration_str": "8.85s", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.01532, "duration_str": "15.32ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 0.173}, {"sql": "select * from `parcours` where `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 122}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.07967, "duration_str": "79.67ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:122", "connection": "imsaaapp", "start_percent": 0.173, "width_percent": 0.9}, {"sql": "select * from `niveaux` where `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 125}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00069, "duration_str": "690μs", "stmt_id": "\\app\\Http\\Livewire\\Result.php:125", "connection": "imsaaapp", "start_percent": 1.073, "width_percent": 0.008}, {"sql": "select * from `semestres` where `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 128}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "\\app\\Http\\Livewire\\Result.php:128", "connection": "imsaaapp", "start_percent": 1.081, "width_percent": 0.008}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 131}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.01662, "duration_str": "16.62ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:131", "connection": "imsaaapp", "start_percent": 1.089, "width_percent": 0.188}, {"sql": "select `id`, `nom`, `prenom` from `users` where exists (select * from `inscription_students` where `users`.`id` = `inscription_students`.`user_id` and `niveau_id` = '3' and `annee_universitaire_id` = '5' and `parcour_id` in ('1', '2', '3') and `inscription_students`.`deleted_at` is null) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "5", "1", "2", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 148}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.27476999999999996, "duration_str": "275ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:148", "connection": "imsaaapp", "start_percent": 1.276, "width_percent": 3.103}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 349 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 349 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 349 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "349", "1", "349", "2", "349", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.64119, "duration_str": "641ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 4.38, "width_percent": 7.242}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (386, 387, 388) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.016829999999999998, "duration_str": "16.83ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 11.621, "width_percent": 0.19}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 349 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "349"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07352, "duration_str": "73.52ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 11.811, "width_percent": 0.83}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 349 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "349"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00131, "duration_str": "1.31ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 12.642, "width_percent": 0.015}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 349 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "349"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00148, "duration_str": "1.48ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 12.656, "width_percent": 0.017}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 188 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 188 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 188 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "188", "1", "188", "2", "188", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01068, "duration_str": "10.68ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 12.673, "width_percent": 0.121}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00108, "duration_str": "1.08ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 12.794, "width_percent": 0.012}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 188 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "188"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.37972, "duration_str": "380ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 12.806, "width_percent": 4.289}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 188 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "188"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0815, "duration_str": "81.5ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 17.095, "width_percent": 0.92}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 188 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "188"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.055490000000000005, "duration_str": "55.49ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 18.015, "width_percent": 0.627}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 189 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 189 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 189 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "189", "1", "189", "2", "189", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04205, "duration_str": "42.05ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 18.642, "width_percent": 0.475}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0438, "duration_str": "43.8ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 19.117, "width_percent": 0.495}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 189 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "189"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.09562999999999999, "duration_str": "95.63ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 19.611, "width_percent": 1.08}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 189 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "189"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.021070000000000002, "duration_str": "21.07ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 20.691, "width_percent": 0.238}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 189 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "189"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0017800000000000001, "duration_str": "1.78ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 20.929, "width_percent": 0.02}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 192 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 192 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 192 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "192", "1", "192", "2", "192", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00902, "duration_str": "9.02ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 20.949, "width_percent": 0.102}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02275, "duration_str": "22.75ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 21.051, "width_percent": 0.257}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 192 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "192"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01712, "duration_str": "17.12ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 21.308, "width_percent": 0.193}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 192 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "192"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01238, "duration_str": "12.38ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 21.502, "width_percent": 0.14}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 192 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "192"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04463, "duration_str": "44.63ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 21.641, "width_percent": 0.504}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 193 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 193 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 193 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "193", "1", "193", "2", "193", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05274, "duration_str": "52.74ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 22.146, "width_percent": 0.596}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05047, "duration_str": "50.47ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 22.741, "width_percent": 0.57}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 193 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "193"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07134, "duration_str": "71.34ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 23.311, "width_percent": 0.806}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 193 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "193"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03957, "duration_str": "39.57ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 24.117, "width_percent": 0.447}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 193 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "193"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05461, "duration_str": "54.61ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 24.564, "width_percent": 0.617}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 194 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 194 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 194 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "194", "1", "194", "2", "194", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03725, "duration_str": "37.25ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 25.181, "width_percent": 0.421}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.038770000000000006, "duration_str": "38.77ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 25.601, "width_percent": 0.438}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 194 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "194"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.06881999999999999, "duration_str": "68.82ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 26.039, "width_percent": 0.777}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 194 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "194"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.06925, "duration_str": "69.25ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 26.816, "width_percent": 0.782}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 194 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "194"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.08947, "duration_str": "89.47ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 27.599, "width_percent": 1.01}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 195 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 195 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 195 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "195", "1", "195", "2", "195", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.21555000000000002, "duration_str": "216ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 28.609, "width_percent": 2.434}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.045450000000000004, "duration_str": "45.45ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 31.043, "width_percent": 0.513}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 195 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "195"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.06365, "duration_str": "63.65ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 31.557, "width_percent": 0.719}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 195 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "195"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05663, "duration_str": "56.63ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 32.276, "width_percent": 0.64}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 195 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "195"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05417, "duration_str": "54.17ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 32.915, "width_percent": 0.612}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 198 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 198 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 198 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "198", "1", "198", "2", "198", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02283, "duration_str": "22.83ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 33.527, "width_percent": 0.258}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04258, "duration_str": "42.58ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 33.785, "width_percent": 0.481}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 198 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "198"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04221, "duration_str": "42.21ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 34.266, "width_percent": 0.477}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 198 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "198"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05447, "duration_str": "54.47ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 34.742, "width_percent": 0.615}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 198 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "198"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05325, "duration_str": "53.25ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 35.358, "width_percent": 0.601}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 199 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 199 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 199 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "199", "1", "199", "2", "199", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05119, "duration_str": "51.19ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 35.959, "width_percent": 0.578}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.09697, "duration_str": "96.97ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 36.537, "width_percent": 1.095}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 199 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "199"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.06743, "duration_str": "67.43ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 37.632, "width_percent": 0.762}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 199 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "199"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03524, "duration_str": "35.24ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 38.394, "width_percent": 0.398}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 199 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "199"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02484, "duration_str": "24.84ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 38.792, "width_percent": 0.281}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 200 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 200 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 200 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "200", "1", "200", "2", "200", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.030780000000000002, "duration_str": "30.78ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 39.072, "width_percent": 0.348}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03377, "duration_str": "33.77ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 39.42, "width_percent": 0.381}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 200 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "200"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01794, "duration_str": "17.94ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 39.802, "width_percent": 0.203}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 200 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "200"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01382, "duration_str": "13.82ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 40.004, "width_percent": 0.156}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 200 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "200"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01939, "duration_str": "19.39ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 40.16, "width_percent": 0.219}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 201 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 201 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 201 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "201", "1", "201", "2", "201", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02664, "duration_str": "26.64ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 40.379, "width_percent": 0.301}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.13163999999999998, "duration_str": "132ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 40.68, "width_percent": 1.487}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 201 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "201"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.09405, "duration_str": "94.05ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 42.167, "width_percent": 1.062}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 201 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "201"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0014, "duration_str": "1.4ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 43.229, "width_percent": 0.016}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 201 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "201"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0014, "duration_str": "1.4ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 43.245, "width_percent": 0.016}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 203 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 203 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 203 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "203", "1", "203", "2", "203", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.08719, "duration_str": "87.19ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 43.261, "width_percent": 0.985}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00157, "duration_str": "1.57ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 44.245, "width_percent": 0.018}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 203 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "203"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07944, "duration_str": "79.44ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 44.263, "width_percent": 0.897}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 203 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "203"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02734, "duration_str": "27.34ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 45.16, "width_percent": 0.309}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 203 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "203"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.15477000000000002, "duration_str": "155ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 45.469, "width_percent": 1.748}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 204 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 204 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 204 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "204", "1", "204", "2", "204", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.08061, "duration_str": "80.61ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 47.217, "width_percent": 0.91}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04635, "duration_str": "46.35ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 48.128, "width_percent": 0.523}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 204 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "204"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.10632, "duration_str": "106ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 48.651, "width_percent": 1.201}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 204 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "204"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.10517, "duration_str": "105ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 49.852, "width_percent": 1.188}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 204 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "204"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.06662, "duration_str": "66.62ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 51.04, "width_percent": 0.752}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 207 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 207 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 207 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "207", "1", "207", "2", "207", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03158, "duration_str": "31.58ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 51.792, "width_percent": 0.357}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04897, "duration_str": "48.97ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 52.149, "width_percent": 0.553}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 207 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "207"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.030449999999999998, "duration_str": "30.45ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 52.702, "width_percent": 0.344}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 207 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "207"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01692, "duration_str": "16.92ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 53.046, "width_percent": 0.191}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 207 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "207"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0061600000000000005, "duration_str": "6.16ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 53.237, "width_percent": 0.07}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 208 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 208 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 208 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "208", "1", "208", "2", "208", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01564, "duration_str": "15.64ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 53.306, "width_percent": 0.177}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (386, 387, 388) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00623, "duration_str": "6.23ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 53.483, "width_percent": 0.07}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 208 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "208"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.006849999999999999, "duration_str": "6.85ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 53.553, "width_percent": 0.077}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 208 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "208"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01079, "duration_str": "10.79ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 53.631, "width_percent": 0.122}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 208 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "208"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01723, "duration_str": "17.23ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 53.753, "width_percent": 0.195}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 213 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 213 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 213 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "213", "1", "213", "2", "213", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.12323, "duration_str": "123ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 53.947, "width_percent": 1.392}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (377, 378, 379) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00099, "duration_str": "990μs", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 55.339, "width_percent": 0.011}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (630, 631, 632, 633, 634) and `user_id` = 213 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "213"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.08141, "duration_str": "81.41ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 55.35, "width_percent": 0.919}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (630, 631, 632, 633, 634) and `user_id` = 213 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "213"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.14149, "duration_str": "141ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 56.27, "width_percent": 1.598}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (630, 631, 632, 633, 634) and `user_id` = 213 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "213"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01225, "duration_str": "12.25ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 57.868, "width_percent": 0.138}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 215 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 215 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 215 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "215", "1", "215", "2", "215", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03468, "duration_str": "34.68ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 58.006, "width_percent": 0.392}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (377, 378, 379) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.10762000000000001, "duration_str": "108ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 58.398, "width_percent": 1.215}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (630, 631, 632, 633, 634) and `user_id` = 215 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "215"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.35992, "duration_str": "360ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 59.613, "width_percent": 4.065}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (630, 631, 632, 633, 634) and `user_id` = 215 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "215"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.34520999999999996, "duration_str": "345ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 63.678, "width_percent": 3.899}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (630, 631, 632, 633, 634) and `user_id` = 215 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "215"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.30057999999999996, "duration_str": "301ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 67.577, "width_percent": 3.395}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 216 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 216 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 216 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "216", "1", "216", "2", "216", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07843000000000001, "duration_str": "78.43ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 70.972, "width_percent": 0.886}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (377, 378, 379) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.08853, "duration_str": "88.53ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 71.857, "width_percent": 1}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (630, 631, 632, 633, 634) and `user_id` = 216 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "216"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05293, "duration_str": "52.93ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 72.857, "width_percent": 0.598}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (630, 631, 632, 633, 634) and `user_id` = 216 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "216"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0681, "duration_str": "68.1ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 73.455, "width_percent": 0.769}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (630, 631, 632, 633, 634) and `user_id` = 216 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "216"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.047009999999999996, "duration_str": "47.01ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 74.224, "width_percent": 0.531}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 217 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 217 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 217 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "217", "1", "217", "2", "217", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04664, "duration_str": "46.64ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 74.755, "width_percent": 0.527}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (377, 378, 379) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00615, "duration_str": "6.15ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 75.282, "width_percent": 0.069}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (630, 631, 632, 633, 634) and `user_id` = 217 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "217"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03434, "duration_str": "34.34ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 75.351, "width_percent": 0.388}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (630, 631, 632, 633, 634) and `user_id` = 217 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "217"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.10601999999999999, "duration_str": "106ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 75.739, "width_percent": 1.197}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (630, 631, 632, 633, 634) and `user_id` = 217 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "217"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.045579999999999996, "duration_str": "45.58ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 76.937, "width_percent": 0.515}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 222 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 222 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 222 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "222", "1", "222", "2", "222", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.09073, "duration_str": "90.73ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 77.451, "width_percent": 1.025}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (386, 387, 388) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0009599999999999999, "duration_str": "960μs", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 78.476, "width_percent": 0.011}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 222 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "222"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.21353999999999998, "duration_str": "214ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 78.487, "width_percent": 2.412}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 222 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "222"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07978, "duration_str": "79.78ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 80.899, "width_percent": 0.901}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 222 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "222"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01617, "duration_str": "16.17ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 81.8, "width_percent": 0.183}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 223 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 223 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 223 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "223", "1", "223", "2", "223", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07464, "duration_str": "74.64ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 81.982, "width_percent": 0.843}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (386, 387, 388) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05324, "duration_str": "53.24ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 82.825, "width_percent": 0.601}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 223 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "223"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05114, "duration_str": "51.14ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 83.427, "width_percent": 0.578}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 223 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "223"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02229, "duration_str": "22.29ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 84.004, "width_percent": 0.252}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 223 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "223"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03756, "duration_str": "37.56ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 84.256, "width_percent": 0.424}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 224 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 224 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 224 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "224", "1", "224", "2", "224", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.039869999999999996, "duration_str": "39.87ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 84.68, "width_percent": 0.45}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (386, 387, 388) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05899, "duration_str": "58.99ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 85.13, "width_percent": 0.666}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 224 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "224"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.044020000000000004, "duration_str": "44.02ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 85.797, "width_percent": 0.497}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 224 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "224"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.06303, "duration_str": "63.03ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 86.294, "width_percent": 0.712}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 224 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "224"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.06706000000000001, "duration_str": "67.06ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 87.006, "width_percent": 0.757}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 225 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 225 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 225 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "225", "1", "225", "2", "225", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.046939999999999996, "duration_str": "46.94ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 87.763, "width_percent": 0.53}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (386, 387, 388) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04277, "duration_str": "42.77ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 88.293, "width_percent": 0.483}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 225 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "225"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03139, "duration_str": "31.39ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 88.776, "width_percent": 0.355}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 225 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "225"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.011380000000000001, "duration_str": "11.38ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 89.131, "width_percent": 0.129}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 225 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "225"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.018269999999999998, "duration_str": "18.27ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 89.259, "width_percent": 0.206}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 226 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 226 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 226 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "226", "1", "226", "2", "226", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03536, "duration_str": "35.36ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 89.466, "width_percent": 0.399}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (386, 387, 388) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0453, "duration_str": "45.3ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 89.865, "width_percent": 0.512}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 226 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "226"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04211, "duration_str": "42.11ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 90.377, "width_percent": 0.476}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 226 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "226"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.10808, "duration_str": "108ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 90.852, "width_percent": 1.221}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 226 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "226"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07442, "duration_str": "74.42ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 92.073, "width_percent": 0.841}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 227 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 227 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 227 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "227", "1", "227", "2", "227", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.059840000000000004, "duration_str": "59.84ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 92.913, "width_percent": 0.676}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0032400000000000003, "duration_str": "3.24ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 93.589, "width_percent": 0.037}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 227 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "227"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02346, "duration_str": "23.46ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 93.626, "width_percent": 0.265}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 227 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "227"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00435, "duration_str": "4.35ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 93.891, "width_percent": 0.049}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 227 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "227"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02095, "duration_str": "20.95ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 93.94, "width_percent": 0.237}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 228 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 228 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 228 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "228", "1", "228", "2", "228", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01943, "duration_str": "19.43ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 94.176, "width_percent": 0.219}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (386, 387, 388) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00404, "duration_str": "4.04ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 94.396, "width_percent": 0.046}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 228 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "228"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02521, "duration_str": "25.21ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 94.442, "width_percent": 0.285}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 228 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "228"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04886, "duration_str": "48.86ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 94.726, "width_percent": 0.552}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 228 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "228"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04469, "duration_str": "44.69ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 95.278, "width_percent": 0.505}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 393 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 393 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 393 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "393", "1", "393", "2", "393", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.20637, "duration_str": "206ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 95.783, "width_percent": 2.331}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (386, 387, 388) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.06928, "duration_str": "69.28ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 98.114, "width_percent": 0.782}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 393 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "393"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.033100000000000004, "duration_str": "33.1ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 98.896, "width_percent": 0.374}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 393 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "393"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02993, "duration_str": "29.93ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 99.27, "width_percent": 0.338}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (645, 646, 647, 648, 649) and `user_id` = 393 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "393"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03472, "duration_str": "34.72ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 99.608, "width_percent": 0.392}]}, "models": {"data": {"App\\Models\\Note": 423, "App\\Models\\Matiere": 135, "App\\Models\\Ue": 81, "App\\Models\\AnneeUniversitaire": 6, "App\\Models\\Semestre": 10, "App\\Models\\Niveau": 5, "App\\Models\\Parcour": 24, "App\\Models\\User": 28}, "count": 712}, "livewire": {"data": {"result #VydCn5IqgicFrMfX6YOk": "array:5 [\n  \"data\" => array:7 [\n    \"newResults\" => array:4 [\n      \"parcour_id\" => array:3 [\n        0 => \"1\"\n        1 => \"2\"\n        2 => \"3\"\n      ]\n      \"niveau_id\" => \"3\"\n      \"semestre_id\" => array:1 [\n        0 => \"5\"\n      ]\n      \"annee_universitaire_id\" => \"5\"\n    ]\n    \"notes\" => array:27 [\n      0 => array:6 [\n        \"nom\" => \"TSARALAZA \"\n        \"prenom\" => \"Vonindraozy Anouchka Norah\"\n        \"moy_raw\" => 15.083333333333\n        \"moy\" => \"15.08\"\n        \"mention\" => \"Bien\"\n        \"rang\" => 1\n      ]\n      1 => array:6 [\n        \"nom\" => \"EMILE\"\n        \"prenom\" => \"Yohan SOARAVO\"\n        \"moy_raw\" => 14.333333333333\n        \"moy\" => \"14.33\"\n        \"mention\" => \"Bien\"\n        \"rang\" => 2\n      ]\n      2 => array:6 [\n        \"nom\" => \"JAOSOLO\"\n        \"prenom\" => \"Youssouf Sahara\"\n        \"moy_raw\" => 14.041666666667\n        \"moy\" => \"14.04\"\n        \"mention\" => \"Bien\"\n        \"rang\" => 3\n      ]\n      3 => array:6 [\n        \"nom\" => \"RAKOTONDRAFARA \"\n        \"prenom\" => \"Mendrika Koloina\"\n        \"moy_raw\" => 13.916666666667\n        \"moy\" => \"13.92\"\n        \"mention\" => \"Assez-bien\"\n        \"rang\" => 4\n      ]\n      4 => array:6 [\n        \"nom\" => \"ZAFY \"\n        \"prenom\" => \"Angela Nathalie\"\n        \"moy_raw\" => 13.708333333333\n        \"moy\" => \"13.71\"\n        \"mention\" => \"Assez-bien\"\n        \"rang\" => 5\n      ]\n      5 => array:6 [\n        \"nom\" => \"FLORICIA \"\n        \"prenom\" => \"Marie Daniela\"\n        \"moy_raw\" => 13.0625\n        \"moy\" => \"13.06\"\n        \"mention\" => \"Assez-bien\"\n        \"rang\" => 6\n      ]\n      6 => array:6 [\n        \"nom\" => \"LOVA\"\n        \"prenom\" => \"Faniva Tsimaholy\"\n        \"moy_raw\" => 12.916666666667\n        \"moy\" => \"12.92\"\n        \"mention\" => \"Assez-bien\"\n        \"rang\" => 7\n      ]\n      7 => array:6 [\n        \"nom\" => \"RANARY \"\n        \"prenom\" => \"Henrista Larina\"\n        \"moy_raw\" => 12.833333333333\n        \"moy\" => \"12.83\"\n        \"mention\" => \"Assez-bien\"\n        \"rang\" => 8\n      ]\n      8 => array:6 [\n        \"nom\" => \"ANDRIAMAKA \"\n        \"prenom\" => \"Mahandry Tihary Mathieu\"\n        \"moy_raw\" => 12.333333333333\n        \"moy\" => \"12.33\"\n        \"mention\" => \"Assez-bien\"\n        \"rang\" => 9\n      ]\n      9 => array:6 [\n        \"nom\" => \"RAHERIMANANA\"\n        \"prenom\" => \"Laurencia\"\n        \"moy_raw\" => 12.208333333333\n        \"moy\" => \"12.21\"\n        \"mention\" => \"Assez-bien\"\n        \"rang\" => 10\n      ]\n      10 => array:6 [\n        \"nom\" => \"GAMILA \"\n        \"prenom\" => \"Velonjara \"\n        \"moy_raw\" => 11.833333333333\n        \"moy\" => \"11.83\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 11\n      ]\n      11 => array:6 [\n        \"nom\" => \"ROTET \"\n        \"prenom\" => \"Tsiaraso Fabio Claudel\"\n        \"moy_raw\" => 11.666666666667\n        \"moy\" => \"11.67\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 12\n      ]\n      12 => array:6 [\n        \"nom\" => \"RAVELOMIRANA\"\n        \"prenom\" => \"Lauriana Sylvanna\"\n        \"moy_raw\" => 11.458333333333\n        \"moy\" => \"11.46\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 13\n      ]\n      13 => array:6 [\n        \"nom\" => \"RAZAFINDRAZANANY\"\n        \"prenom\" => \"Marnella\"\n        \"moy_raw\" => 11.041666666667\n        \"moy\" => \"11.04\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 14\n      ]\n      14 => array:6 [\n        \"nom\" => \"SOA \"\n        \"prenom\" => \"Philipine Causta\"\n        \"moy_raw\" => 10.875\n        \"moy\" => \"10.88\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 15\n      ]\n      15 => array:6 [\n        \"nom\" => \"RAZAFIMBELO\"\n        \"prenom\" => \"Marie Stella Philippe\"\n        \"moy_raw\" => 10.833333333333\n        \"moy\" => \"10.83\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 16\n      ]\n      16 => array:6 [\n        \"nom\" => \"RAZAFY \"\n        \"prenom\" => \"Marie Antoniesca\"\n        \"moy_raw\" => 10.541666666667\n        \"moy\" => \"10.54\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 17\n      ]\n      17 => array:6 [\n        \"nom\" => \"TOTOFENO\"\n        \"prenom\" => \" Leticia Caprima\"\n        \"moy_raw\" => 9.875\n        \"moy\" => \"9.88\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 18\n      ]\n      18 => array:6 [\n        \"nom\" => \"TREFINDRAZANA \"\n        \"prenom\" => \"Ricina\"\n        \"moy_raw\" => 9.7083333333333\n        \"moy\" => \"9.71\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 19\n      ]\n      19 => array:6 [\n        \"nom\" => \"LASALMONIE\"\n        \"prenom\" => \"Joana Richina\"\n        \"moy_raw\" => 9.5\n        \"moy\" => \"9.50\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 20\n      ]\n      20 => array:6 [\n        \"nom\" => \"NIRINA \"\n        \"prenom\" => \"Rozia\"\n        \"moy_raw\" => 9.3125\n        \"moy\" => \"9.31\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 21\n      ]\n      21 => array:6 [\n        \"nom\" => \"MANOROSOA\"\n        \"prenom\" => \"Aboudou Sandra\"\n        \"moy_raw\" => 8.375\n        \"moy\" => \"8.38\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 22\n      ]\n      22 => array:6 [\n        \"nom\" => \"SAGNITRY\"\n        \"prenom\" => \"Rachida Adrianah\"\n        \"moy_raw\" => 8.2083333333333\n        \"moy\" => \"8.21\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 23\n      ]\n      23 => array:6 [\n        \"nom\" => \"IASILANY\"\n        \"prenom\" => \"Prisco\"\n        \"moy_raw\" => 7.5416666666667\n        \"moy\" => \"7.54\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 24\n      ]\n      24 => array:6 [\n        \"nom\" => \"MAMORY\"\n        \"prenom\" => \"Andy Stanley\"\n        \"moy_raw\" => 5.9583333333333\n        \"moy\" => \"5.96\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 25\n      ]\n      25 => array:6 [\n        \"nom\" => \"RAKOTOZANAKA\"\n        \"prenom\" => \"Anelka\"\n        \"moy_raw\" => 4.9166666666667\n        \"moy\" => \"4.92\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 26\n      ]\n      26 => array:6 [\n        \"nom\" => \"HOLIFARA\"\n        \"prenom\" => \"Andrianina Jaura\"\n        \"moy_raw\" => 3.3333333333333\n        \"moy\" => \"3.33\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 27\n      ]\n    ]\n    \"showResults\" => true\n    \"current_parcours\" => Illuminate\\Database\\Eloquent\\Collection {#3117\n      #items: array:3 [\n        0 => App\\Models\\Parcour {#1425\n          #connection: \"mysql\"\n          #table: \"parcours\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:7 [\n            \"id\" => 1\n            \"sigle\" => \"THR\"\n            \"nom\" => \"Tourisme Hôtellerie et Restauration\"\n            \"mention_id\" => 4\n            \"created_at\" => null\n            \"updated_at\" => null\n            \"deleted_at\" => null\n          ]\n          #original: array:7 [\n            \"id\" => 1\n            \"sigle\" => \"THR\"\n            \"nom\" => \"Tourisme Hôtellerie et Restauration\"\n            \"mention_id\" => 4\n            \"created_at\" => null\n            \"updated_at\" => null\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:3 [\n            0 => \"nom\"\n            1 => \"sigle\"\n            2 => \"mention_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        1 => App\\Models\\Parcour {#1679\n          #connection: \"mysql\"\n          #table: \"parcours\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:7 [\n            \"id\" => 2\n            \"sigle\" => \"TBA\"\n            \"nom\" => \"Techniques Bancaire et Assurance\"\n            \"mention_id\" => 1\n            \"created_at\" => null\n            \"updated_at\" => null\n            \"deleted_at\" => null\n          ]\n          #original: array:7 [\n            \"id\" => 2\n            \"sigle\" => \"TBA\"\n            \"nom\" => \"Techniques Bancaire et Assurance\"\n            \"mention_id\" => 1\n            \"created_at\" => null\n            \"updated_at\" => null\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:3 [\n            0 => \"nom\"\n            1 => \"sigle\"\n            2 => \"mention_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n        2 => App\\Models\\Parcour {#1720\n          #connection: \"mysql\"\n          #table: \"parcours\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:7 [\n            \"id\" => 3\n            \"sigle\" => \"CM\"\n            \"nom\" => \"Communication et Marketing\"\n            \"mention_id\" => 5\n            \"created_at\" => null\n            \"updated_at\" => null\n            \"deleted_at\" => null\n          ]\n          #original: array:7 [\n            \"id\" => 3\n            \"sigle\" => \"CM\"\n            \"nom\" => \"Communication et Marketing\"\n            \"mention_id\" => 5\n            \"created_at\" => null\n            \"updated_at\" => null\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:3 [\n            0 => \"nom\"\n            1 => \"sigle\"\n            2 => \"mention_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"current_niveau\" => App\\Models\\Niveau {#1765\n      #connection: \"mysql\"\n      #table: \"niveaux\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:4 [\n        \"id\" => 3\n        \"nom\" => \"3ème année\"\n        \"sigle\" => \"L3\"\n        \"deleted_at\" => null\n      ]\n      #original: array:4 [\n        \"id\" => 3\n        \"nom\" => \"3ème année\"\n        \"sigle\" => \"L3\"\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"current_semestres\" => Illuminate\\Database\\Eloquent\\Collection {#4100\n      #items: array:1 [\n        0 => App\\Models\\Semestre {#1782\n          #connection: \"mysql\"\n          #table: \"semestres\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 5\n            \"nom\" => \"Semestre 5\"\n            \"niveau_id\" => 3\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 5\n            \"nom\" => \"Semestre 5\"\n            \"niveau_id\" => 3\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"niveau_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"current_annee\" => App\\Models\\AnneeUniversitaire {#1797\n      #connection: \"mysql\"\n      #table: \"annee_universitaires\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:3 [\n        \"id\" => 5\n        \"nom\" => \"2023/2024\"\n        \"deleted_at\" => null\n      ]\n      #original: array:3 [\n        \"id\" => 5\n        \"nom\" => \"2023/2024\"\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: false\n      #hidden: []\n      #visible: []\n      #fillable: array:1 [\n        0 => \"nom\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n  ]\n  \"name\" => \"result\"\n  \"view\" => \"livewire.deraq.resultat.index\"\n  \"component\" => \"App\\Http\\Livewire\\Result\"\n  \"id\" => \"VydCn5IqgicFrMfX6YOk\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "HDK0mRbkDvv2cdX03ve8fTJp3eXq8ixJSlVRbhnW", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pedagogiques/resultat\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1752562641\n]"}, "request": {"path_info": "/livewire/message/result", "status_code": "<pre class=sf-dump id=sf-dump-1209552877 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1209552877\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-966925043 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-966925043\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1176488976 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">VydCn5IqgicFrMfX6YOk</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">result</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"21 characters\">pedagogiques/resultat</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">27192c78</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>newResults</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>parcour_id</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str>2</span>\"\n          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str>3</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>niveau_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>semestre_id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>5</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>annee_universitaire_id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>notes</span>\" => []\n      \"<span class=sf-dump-key>showResults</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>current_parcours</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>current_niveau</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>current_semestres</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>current_annee</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">625589445d2cbdb7180595a786235001cecb1ffcc767f8617be3090b5188968e</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">p5yq</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">generateResults</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1176488976\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2085500016 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">622</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">HDK0mRbkDvv2cdX03ve8fTJp3eXq8ixJSlVRbhnW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/pedagogiques/resultat</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjNhOHRsaThlamZKMlBmTXcySllVK3c9PSIsInZhbHVlIjoiSENER1Y5cUtMcU5jVzhFT21yQlFQQ3NaemU1bjBLM2NqYVh6MFl4eGF3S2dHRFQ2UFNkOFRFZ1FqT1RxUHpxSHFvMDFHS29xcGw0MUNGUExhZmxacFRrMHhSMyt1bTBTbFBWaDNDTjJQdGxpSUdCTGRIQVVDb2JhdVJwaVErWGYiLCJtYWMiOiIzYzdmY2MwZGU1NmM1MDgwNjI3YmIxZWZlMzc2MTMxMjYwMTIwZTQzZjhiZjE3NTRiMDlmN2U3MjUwNGI5NTI5IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IjdIaytzNVpZMzBJTTVKdEo2U2N3NUE9PSIsInZhbHVlIjoibklHWlRabUV0TFVOY3JEM21XL3N3L1l0dFFaczFYakQzVWFTL1JzanBwSy9wUDFuNFN0T3dSRmhrQ3M4NVZXUVRxU3p3ZG9XYTJuNk1HaEo2dzRpRWQ1aERXSUV2b3d4SHUrNlh4Nm5UYXVnS3pJUm9nM1h4SHBUUlNEanFaQk8iLCJtYWMiOiIyZGJjODU3YWU1ZDVjOWI1ODI0MWY4MjAwZTIxMjg0OTYzNjc5YjNiOTIyZDA5Y2MyZDEzZGExN2RhYjAwNTJkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2085500016\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1133248889 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">59141</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"24 characters\">/livewire/message/result</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"24 characters\">/livewire/message/result</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"34 characters\">/index.php/livewire/message/result</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">622</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">622</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/pedagogiques/resultat</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjNhOHRsaThlamZKMlBmTXcySllVK3c9PSIsInZhbHVlIjoiSENER1Y5cUtMcU5jVzhFT21yQlFQQ3NaemU1bjBLM2NqYVh6MFl4eGF3S2dHRFQ2UFNkOFRFZ1FqT1RxUHpxSHFvMDFHS29xcGw0MUNGUExhZmxacFRrMHhSMyt1bTBTbFBWaDNDTjJQdGxpSUdCTGRIQVVDb2JhdVJwaVErWGYiLCJtYWMiOiIzYzdmY2MwZGU1NmM1MDgwNjI3YmIxZWZlMzc2MTMxMjYwMTIwZTQzZjhiZjE3NTRiMDlmN2U3MjUwNGI5NTI5IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IjdIaytzNVpZMzBJTTVKdEo2U2N3NUE9PSIsInZhbHVlIjoibklHWlRabUV0TFVOY3JEM21XL3N3L1l0dFFaczFYakQzVWFTL1JzanBwSy9wUDFuNFN0T3dSRmhrQ3M4NVZXUVRxU3p3ZG9XYTJuNk1HaEo2dzRpRWQ1aERXSUV2b3d4SHUrNlh4Nm5UYXVnS3pJUm9nM1h4SHBUUlNEanFaQk8iLCJtYWMiOiIyZGJjODU3YWU1ZDVjOWI1ODI0MWY4MjAwZTIxMjg0OTYzNjc5YjNiOTIyZDA5Y2MyZDEzZGExN2RhYjAwNTJkIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1752562714.7193</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1752562714</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1133248889\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1792799777 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HDK0mRbkDvv2cdX03ve8fTJp3eXq8ixJSlVRbhnW</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lWz8fwnB8ZvveFIAdF4XqrkKmMmueLxxZxRrzgVU</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1792799777\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-176958404 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 15 Jul 2025 06:58:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImdoWjQ1aFFwd3NmYjlSMFRqSWZ1VVE9PSIsInZhbHVlIjoiOEZCU0hzZmJ2OWlKSS9ZMVJ3S2gxSFdYbE1vOERjL3RpQzk3Z0E2UnVYd1czYmF5UkJlY1pRaWI0VGIzV3B0MjhLR0luRG1IajBEcWtCaXMxWWJiRzFsTVRnYVNmVWRYTGhmWjdoN2hzV3QybXNvSjZ0NHN1bE4wRS9xbjNQRU4iLCJtYWMiOiIwMTU1Mzg3MjQwNWY1MDMwZTc2NTg4OTZjZWU1YWExYmQyMDFmODk3YWRiZTdjMzQ4ODA2ZmZlOWIxMTAwZDE0IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 08:58:46 GMT; Max-Age=7199; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6Ill3d0pmQUhGd0hRQndIYTVqcTkva1E9PSIsInZhbHVlIjoiem10VXRpVnlDbVFzaVdwS3pFWkh5TW9tblpLWThEV1FMZlJYOHJwRkJSTjdndTkxQ3JVQk9sWVlvVlBKZXNUVER2a2dBMGZsYkRnMHowL01TQUpWL0FhS0tVRmk2SXpFSVNtOS9EVjBNNWdpc1kxblBQYjlnSEdmL2l3c3Jic08iLCJtYWMiOiI2ZGQwNDA2NDcxMzBlODQ0ZDBhZDI0NmZmNzA0NjlhODE2NTIyOWE1NjhiOTQxMmQ1NWM1MjY0NjY0NWMyNjJkIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 08:58:46 GMT; Max-Age=7199; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImdoWjQ1aFFwd3NmYjlSMFRqSWZ1VVE9PSIsInZhbHVlIjoiOEZCU0hzZmJ2OWlKSS9ZMVJ3S2gxSFdYbE1vOERjL3RpQzk3Z0E2UnVYd1czYmF5UkJlY1pRaWI0VGIzV3B0MjhLR0luRG1IajBEcWtCaXMxWWJiRzFsTVRnYVNmVWRYTGhmWjdoN2hzV3QybXNvSjZ0NHN1bE4wRS9xbjNQRU4iLCJtYWMiOiIwMTU1Mzg3MjQwNWY1MDMwZTc2NTg4OTZjZWU1YWExYmQyMDFmODk3YWRiZTdjMzQ4ODA2ZmZlOWIxMTAwZDE0IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 08:58:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6Ill3d0pmQUhGd0hRQndIYTVqcTkva1E9PSIsInZhbHVlIjoiem10VXRpVnlDbVFzaVdwS3pFWkh5TW9tblpLWThEV1FMZlJYOHJwRkJSTjdndTkxQ3JVQk9sWVlvVlBKZXNUVER2a2dBMGZsYkRnMHowL01TQUpWL0FhS0tVRmk2SXpFSVNtOS9EVjBNNWdpc1kxblBQYjlnSEdmL2l3c3Jic08iLCJtYWMiOiI2ZGQwNDA2NDcxMzBlODQ0ZDBhZDI0NmZmNzA0NjlhODE2NTIyOWE1NjhiOTQxMmQ1NWM1MjY0NjY0NWMyNjJkIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 08:58:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-176958404\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-935393290 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HDK0mRbkDvv2cdX03ve8fTJp3eXq8ixJSlVRbhnW</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/pedagogiques/resultat</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1752562641</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-935393290\", {\"maxDepth\":0})</script>\n"}}