<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\Niveau;
use App\Models\Parcour;
use App\Models\Semestre;
use App\Models\Ue;
use App\Models\User;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Database\Eloquent\Collection;
use Livewire\Component;
use Illuminate\Support\Facades\View;

class Result extends Component
{
    /**
     * Form inputs
     */
    public $newResults = [
        'parcour_id' => [],  // Changed to array for multi-select
        'niveau_id' => 0,
        'semestre_id' => [], // Changed to array for multi-select
        'annee_universitaire_id' => 0
    ];
    
    /**
     * Results data
     */
    public $notes = [];
    public $showResults = false;
    
    /**
     * Current selections for display
     */
    public $current_parcours;
    public $current_niveau;
    public $current_semestres;
    public $current_annee;
    
    /**
     * Cached data to reduce database calls
     */
    protected $parcours;
    protected $niveaux;
    protected $semestres;
    protected $annees;

    /**
     * Lifecycle hook for initializing the component
     */
    public function mount()
    {
        // Cache commonly accessed data
        $this->parcours = Parcour::all();
        $this->niveaux = Niveau::all();
        $this->semestres = Semestre::all();
        $this->annees = AnneeUniversitaire::all();
    }

    /**
     * Render the component
     */
    public function render()
    {
         // Ensure parcours is never null by using the cached property or re-fetching
        $parcours = $this->parcours ?? Parcour::all();
        $niveaux = $this->niveaux ?? Niveau::all();
        $semestres = $this->semestres ?? Semestre::all();
        $annees = $this->annees ?? AnneeUniversitaire::all();

        return view('livewire.deraq.resultat.index', [
            "parcours" => $parcours,
            "niveaux" => $niveaux,
            "semestres" => $semestres,
            "annees" => $annees
            ])
        ->extends('layouts.backend')
        ->section('content');
    }

    /**
     * Define validation rules
     */
    public function rules()
    {
        return [
            'newResults.niveau_id' => 'required|not_in:0',
            'newResults.parcour_id' => 'required|array|min:1',
            'newResults.semestre_id' => 'required|array|min:1',
            'newResults.annee_universitaire_id' => 'required|not_in:0',
        ];
    }

    /**
     * Reset all form fields and results
     */
    public function resetResults()
    {
        $this->notes = [];
        $this->showResults = false;
        $this->newResults = [
            'parcour_id' => [],
            'niveau_id' => 0,
            'semestre_id' => [],
            'annee_universitaire_id' => 0
        ];
        
        $this->dispatchBrowserEvent('reset-select2');
    }

    /**
     * Generate results based on selected criteria
     */
    public function generateResults()
    {
        $this->validate();
        
        // Make sure parcours and other collections are available
        if (!$this->parcours) {
            $this->parcours = Parcour::all();
        }
        if (!$this->niveaux) {
            $this->niveaux = Niveau::all();
        }
        if (!$this->semestres) {
            $this->semestres = Semestre::all();
        }
        if (!$this->annees) {
            $this->annees = AnneeUniversitaire::all();
        }
        
        // Store current selections for display with null checks
        $parcourIds = $this->newResults['parcour_id'] ?? [];
        $this->current_parcours = $this->parcours ? $this->parcours->whereIn('id', $parcourIds)->values() : collect();
        $this->current_niveau = $this->niveaux ? $this->niveaux->firstWhere('id', $this->newResults['niveau_id']) : null;
        
        $semestreIds = $this->newResults['semestre_id'] ?? [];
        $this->current_semestres = $this->semestres ? $this->semestres->whereIn('id', $semestreIds)->values() : collect();
        $this->current_annee = $this->annees ? $this->annees->firstWhere('id', $this->newResults['annee_universitaire_id']) : null;
    
        // Build query
        $query = User::query()->whereHas('info', function($q) {
            $q->where('niveau_id', $this->newResults['niveau_id'])
              ->where('annee_universitaire_id', $this->newResults['annee_universitaire_id'])
              ->whereIn('parcour_id', $this->newResults['parcour_id'] ?? []);
        })->get(['id', 'nom', 'prenom']);
    
        // Calculate results
        $this->calculateResults($query);
        
        // Show results section
        $this->showResults = true;
        
        // Scroll to results
        $this->dispatchBrowserEvent('scroll-to-results');
    }

    /**
     * Calculate student results
     * 
     * @param Collection $users Students to calculate results for
     */
    public function calculateResults(Collection $users)
{
    $this->notes = [];
    $anneeId = $this->newResults['annee_universitaire_id'];
    $semestreIds = $this->newResults['semestre_id'];
    
    foreach ($users as $user) {
        // Use query similar to original code
        $ues = Ue::query()
            ->whereIn('semestre_id', $semestreIds)
            ->where('annee_universitaire_id', $anneeId)
            ->withWhereHas('matiere.notesP1', fn ($q) => $q->whereUserId($user->id))
            ->withWhereHas('matiere.notesP2', fn ($q) => $q->whereUserId($user->id))
            ->withWhereHas('matiere.notesExam', fn ($q) => $q->whereUserId($user->id))
            ->with(['matiere'])
            ->get();
            
        if ($ues->isEmpty()) {
            continue;
        }
        
        $moyUe = 0;
        $j = 0;
            
        foreach ($ues as $ec) {
            $total = 0;
            $matCount = $ec->matiere->count();
            
            if ($matCount == 0) continue;
            
            for ($i = 0; $i < $matCount; $i++) {
                $total += $ec->matiere[$i]->moyenne;
            }
            
            $moyUe += $total / $matCount;
            $j++;
        }
        
        // Calculate final average without formatting to prevent rounding issues
        $moy = $j > 0 ? $moyUe / $j : 0;
        
        // Use the separate mention method for consistency
        $mention = $this->determineMention($moy);
        
        // Format only when adding to notes array
        $this->notes[] = [
            "nom" => $user->nom, 
            "prenom" => $user->prenom, 
            "moy" => number_format($moy, 2), 
            "mention" => $mention
        ];
    }
    
    // Sort by average (highest first)
    if (!empty($this->notes)) {
        array_multisort(array_column($this->notes, 'moy'), SORT_DESC, $this->notes);
    }
}
    
    /**
     * Determine mention based on average score
     * 
     * @param float $average Student's average score
     * @return string Mention text
     */
    protected function determineMention($average)
    {
        if ($average >= 16) return "Très bien";
        if ($average >= 14) return "Bien";
        if ($average >= 12) return "Assez-bien";
        return "Passable";
    }

    /**
     * Generate PDF of results
     */
    public function pdfGenerate()
{
    if (empty($this->notes)) {
        $this->dispatchBrowserEvent('show-error', ['message' => 'Aucun résultat à imprimer']);
        return;
    }
    
    // Generate a descriptive filename
    $filename = "resultat";
    // Make sure current_parcours isn't null before iterating
    if ($this->current_parcours) {
        foreach ($this->current_parcours as $parcour) {
            $filename .= "_" . $parcour->sigle;
        }
    }
    $filename .= ".pdf";
    
    // Generate PDF with null checks
    $view = View::make('pdf.resultat', [
        'notes' => $this->notes, 
        'parcours' => $this->current_parcours ?? collect(),
        'niveau' => $this->current_niveau, 
        'semestres' => $this->current_semestres ?? collect(),
        'annee' => $this->current_annee
    ]);
    
    $html = mb_convert_encoding($view, 'HTML-ENTITIES', 'UTF-8');
    $pdfContent = PDF::loadHtml($html)->output();
    
    return response()->streamDownload(
        fn () => print($pdfContent),
        $filename
    );
}
}